<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <!-- 主表单区域 -->
      <a-form :form="form" slot="detail">
        <!-- 第一行 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="qbs名称" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['qbsName', validatorRules.qbsName]" placeholder="请输入qbs名称" autocomplete="off"
                       disabled />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="qbs编码" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input v-decorator="['qbsCode', validatorRules.qbsCode]" placeholder="请输入qbs编码" autocomplete="off"
                       disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第二行 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="验评结果" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag type="list" v-decorator="['evaluationState', validatorRules.evaluationState]"
                                 :trigger-change="true"
                                 dictCode="evaluation_state" placeholder="请选择验评结果" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="验评日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择验评日期"
                v-decorator="['rectificationCompleteDate', validatorRules.rectificationCompleteDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>

        </a-row>
        <a-row>

          <a-col :span="12">
            <a-form-item label="单位工程量" :labelCol="labelCol" :wrapperCol="wrapperCol" style="margin-bottom:0;">
              <a-row type="flex" align="middle">
                <a-col :span="16">
                  <a-input v-decorator="['quantityWork']" placeholder="请输入单位工程量" />
                </a-col>
                <a-col :span="1" style="text-align:center;">&nbsp;</a-col>
                <a-col :span="7">
                  <j-dict-select-tag v-decorator="['quantityWorkUnit']" :trigger-change="true" dictCode="work_unit"
                                     placeholder="单位" style="width: 100%;" />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第三行 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="施工日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-row type="flex" align="middle">
                <a-col :span="11">
                  <j-date placeholder="开始日期"
                          v-decorator="['constructionStartDate', validatorRules.constructionStartDate]"
                          :trigger-change="true"
                          style="width: 100%" />
                </a-col>
                <a-col :span="2" style="text-align:center;line-height:32px;">-</a-col>
                <a-col :span="11">
                  <j-date placeholder="结束日期"
                          v-decorator="['constructionEndDate', validatorRules.constructionEndDate]"
                          :trigger-change="true"
                          style="width: 100%" />
                </a-col>
              </a-row>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="施工单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-select-depart v-decorator="['constructionUnit', validatorRules.constructionUnit]" multi />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第四行 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="图片资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload isMultiple v-decorator="['pictureInformation']" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="视频资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['videoInformation']" :trigger-change="true" />
            </a-form-item>
          </a-col>
        </a-row>
        <!-- 第五行 -->
        <a-row>
          <a-col :span="12">
            <a-form-item label="验评附件资料" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-upload v-decorator="['attachment']" :trigger-change="true" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-item label="上报人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input autocomplete="off" v-decorator="['reporter', validatorRules.reporter]"
                       placeholder="请输入上报人"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人电话" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['reporterPhone', validatorRules.reporterPhone]"
                placeholder="请输入上报人电话"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报日期" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-date
                placeholder="请选择上报日期"
                v-decorator="['reportDate', validatorRules.reportDate]"
                :trigger-change="true"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="上报人单位" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                autocomplete="off"
                v-decorator="['reporterDepart', validatorRules.reporterDepart]"
                placeholder="请选择上报人部门"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12" v-if="false && showSupervisorField">
            <a-form-item label="监理审核人" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                type="list"
                v-decorator="['supervisor', validatorRules.supervisor]"
                :trigger-change="true"
                dictCode="person_info,name,id"
                placeholder="请选择监理审核人"
              />

            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </j-form-container>
    <!-- 子表单区域 -->
    <a-tabs v-model="activeKey" @change="handleChangeTabs">
      <a-tab-pane tab="验评资料详情" :key="refKeys[0]" :forceRender="true">
        <j-editable-table
          ref="qualityEvaluationInformationDetail"
          :loading="qualityEvaluationInformationDetailTable.loading"
          :columns="qualityEvaluationInformationDetailTable.columns"
          :dataSource="qualityEvaluationInformationDetailTable.dataSource"
          :maxHeight="300"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowKey="true"
          :actionButton="true"
          @valueChange="handleValueChange"
        >
          <template slot="action" slot-scope="props">
            <a-popconfirm title="确定删除吗?"
                          @confirm="() => $refs.qualityEvaluationInformationDetail.removeRows(props.rowId)">
              <a-button type="link" :disabled="formDisabled" style="color: #ff4d4f">
                <a-icon type="delete" />
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </j-editable-table>
      </a-tab-pane>
    </a-tabs>
    <a-row v-if="showFlowSubmitButton" style="text-align: center;width: 100%;margin-top: 16px;">
      <a-button @click="handleOk">提 交</a-button>
    </a-row>
  </a-spin>
</template>

<script>

import pick from 'lodash.pick'
import { getAction, httpAction } from '@/api/manage'
import { FormTypes, getRefPromise } from '@/utils/JEditableTableUtil'
import { JEditableTableMixin } from '@/mixins/JEditableTableMixin'
import { getStore } from '@/utils/storage.js'
import moment from 'dayjs'

const pdfjsLib = require('pdfjs-dist/webpack')

export default {
  name: 'QualityEvaluationInformationForm',
  mixins: [JEditableTableMixin],
  components: {},
  data() {
    return {
      form: null,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      labelCol2: {
        xs: { span: 24 },
        sm: { span: 3 }
      },
      wrapperCol2: {
        xs: { span: 24 },
        sm: { span: 20 }
      },
      addDefaultRowNum: 1,
      validatorRules: {
        qbsCode: {
          rules: [
            { required: true, message: '请输入qbs编码!' }
          ]
        },
        qbsName: {
          rules: [
            { required: true, message: '请输入qbs名称!' }
          ]
        },
        evaluationState: {
          rules: [
            { required: true, message: '请选择验评结果!' }
          ]
        },
        rectificationCompleteDate: {
          rules: [{ required: true, message: '请选择验评日期!' }]
        },
        constructionStartDate: {
          rules: [{ required: true, message: '请选择施工开始日期!' }]
        },
        constructionEndDate: {
          rules: [{ required: true, message: '请选择施工结束日期!' }]
        },
        constructionUnit: {
          rules: [{ required: true, message: '请选择施工单位!' }]
        },
        supervisor: {
          rules: [{ required: true, message: '请输入监理审核人!' }]
        },
        reporter: {
          rules: [{ required: true, message: '请输入上报人!' }]
        },
        reporterPhone: {
          rules: [{ required: true, message: '请输入上报人电话!' }]
        },
        reportDate: {
          rules: [{ required: true, message: '请输入上报日期!' }]
        },
        reporterDepart: {
          rules: [{ required: true, message: '请输入上报人单位!' }]
        }
      },
      refKeys: ['qualityEvaluationInformationDetail'],
      tableKeys: ['qualityEvaluationInformationDetail'],
      activeKey: 'qualityEvaluationInformationDetail',
      qualityEvaluationInformationDetailTable: {
        loading: false,
        dataSource: [],
        columns: [
          {
            title: '表单名称',
            key: 'formName',
            type: FormTypes.input,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          },
          {
            title: '页数',
            key: 'filePageSize',
            type: FormTypes.inputNumber,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          },
          {
            title: '验评时间',
            key: 'evaluationDate',
            type: FormTypes.date,
            width: '200px',
            placeholder: '请输入${title}',
            defaultValue: ''
          },
          {
            title: '附件',
            key: 'fileAttachment',
            type: FormTypes.file,
            token: true,
            responseName: 'message',
            width: '200px',
            placeholder: '请选择文件',
            defaultValue: ''
          },
          {
            title: '操作',
            key: 'action',
            type: FormTypes.slot,
            width: '80px',
            scopedSlots: {
              customRender: 'action'
            }
          }
        ]
      },
      url: {
        add: '/quality/qualityEvaluationInformation/add',
        edit: '/quality/qualityEvaluationInformation/edit',
        queryById: '/quality/qualityEvaluationInformation/queryById',
        qualityEvaluationInformationDetail: {
          list: '/quality/qualityEvaluationInformation/queryQualityEvaluationInformationDetailByMainId'
        }
      },
      confirmLoading: false,
      model: {},
      visible: false
    }
  },
  props: {
    //流程表单data
    formData: {
      type: Object,
      default: () => {
      },
      required: false
    },
    //表单模式：false流程表单 true普通表单
    formBpm: {
      type: Boolean,
      default: false,
      required: false
    },
    //表单禁用
    disabled: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  computed: {
    userInfo() {
      return getStore('pro__Login_UserinfoNew')
    },
    apiBaseUrl() {
      return window._CONFIG['domianURL'] || '/jeecg-boot'
    },
    formDisabled() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return false
        }
        return true
      }
      return this.disabled
    },
    showFlowSubmitButton() {
      if (this.formBpm === true) {
        if (this.formData.disabled === false) {
          return true
        }
      }
      return false
    },
    showSupervisorField() {
      // 只有在监理审批环节及之后才显示监理审核人字段
      // 流程状态：1-待提交，2-处理中(监理审批)，3-已完成
      if (this.model && this.model.bpmStatus) {
        return this.model.bpmStatus === '2' || this.model.bpmStatus === '3'
      }
      return false
    },
    isFirstStage() {
      // 判断是否为第一阶段（施工单位发起）
      return !this.model.bpmStatus || this.model.bpmStatus === '1'
    }
  },
  created() {
    this.form = this.$form.createForm(this)
    // 动态设置上传地址
    const fileColumn = this.qualityEvaluationInformationDetailTable.columns.find(col => col.key === 'fileAttachment')
    if (fileColumn) {
      fileColumn.action = this.apiBaseUrl + '/sys/common/upload'
    }
    //如果是流程中表单，则需要加载流程表单data
    this.showFlowData()
  },
  methods: {
    submitForm() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl += this.url.add
            method = 'post'
          } else {
            httpurl += this.url.edit
            method = 'put'
          }
          let formData = Object.assign(this.model, values)
          this.model.details = this.qualityEvaluationInformationDetailTable.dataSource
          console.log('details', this.qualityEvaluationInformationDetailTable.dataSource)
          console.log('表单提交数据', formData)
          httpAction(httpurl, formData, method)
            .then((res) => {
              if (res.success) {
                that.$message.success(res.message)
                that.$emit('ok', res.result)
              } else {
                that.$message.warning(res.message)
              }
            })
            .finally(() => {
              that.confirmLoading = false
            })
        }
      })
    },
    handleValueChange(event) {
      const { column, value, row, target } = event
      if (column.key === 'fileAttachment' && value && value.status === 'done') {
        const fileName = value.responseName || value.name
        const rowKey = row.id
        // 更新表单名称
        target.setValues([{
          rowKey: rowKey,
          values: { formName: fileName }
        }])
        // 只处理pdf
        if (fileName.toLowerCase().endsWith('.pdf')) {
          const fullUrl = this.apiBaseUrl + '/sys/common/static/amlf/' + fileName
          fetch(fullUrl)
            .then(res => res.arrayBuffer())
            .then(arrayBuffer => pdfjsLib.getDocument(arrayBuffer).promise)
            .then(pdf => {
              const pageCount = pdf.numPages
              target.setValues([{
                rowKey: rowKey,
                values: { filePageSize: pageCount }
              }])
            })
            .catch(error => {
              console.error('获取PDF页数失败:', error)
            })
        }
      }
    },
    add() {
      if (this.form) {
        this.form.resetFields()
      }
      this.model = {}
      this.visible = true
      this.qualityEvaluationInformationDetailTable.dataSource = []
      this.$nextTick(() => {
        let fieldval = {}
        this.form.setFieldsValue(fieldval)
      })
    },
    edit(record) {
      console.log('edit', record)
      this.form.resetFields()
      this.model = Object.assign({}, record)
      console.log()
      this.form.setFieldsValue({ qbsId: this.model.qbsId, qbsName: this.model.qbsName, qbsCode: this.model.qbsCode })
      console.log('====================id=====', this.model.id)
      if (this.model.id) {
        this.$nextTick(() => {
          this.form.setFieldsValue(
            pick(
              this.model,
              'qbsName',
              'qbsCode',
              'evaluationState',
              'rectificationCompleteDate',
              'quantityWork',
              'quantityWorkUnit',
              'constructionStartDate',
              'constructionEndDate',
              'constructionUnit',
              'pictureInformation',
              'videoInformation',
              'attachment',
              'supervisor',
              'reporter',
              'reporterPhone',
              'reporterDepart',
              'reportDate'
            )
          )
          console.log('设置表单字段值', this.model)
          // 查询子表数据
          this.loadSubTableData(this.model.id)
        })
      } else {
        this.form.setFieldsValue({ reporter: this.userInfo.workUserName })
        this.form.setFieldsValue({ reporterPhone: this.userInfo.phone })
        this.form.setFieldsValue({ reporterDepart: this.userInfo.workDepartmentName })
        this.form.setFieldsValue({ reportDate: moment(String(new Date())).format('YYYY-MM-DD') })
        this.qualityEvaluationInformationDetailTable.dataSource = []
      }
      this.visible = true
    },
    loadSubTableData(mainId) {
      getAction(this.url.qualityEvaluationInformationDetail.list, { id: mainId }).then(res => {
        if (res.success) {
          this.qualityEvaluationInformationDetailTable.dataSource = res.result || []
        } else {
          this.qualityEvaluationInformationDetailTable.dataSource = []
        }
      })
    },
    getAllTable() {
      let values = this.tableKeys.map(key => getRefPromise(this, key))
      return Promise.all(values)
    },
    /** 整理成formData */
    classifyIntoFormData(allValues) {
      let main = Object.assign(this.model, allValues.formValue)
      return {
        ...main, // 展开
        details: allValues.tablesValue[0].values
      }
    },
    //渲染流程表单数据
    showFlowData() {
      if (this.formBpm === true) {
        let params = { id: this.formData.dataId }
        getAction(this.url.queryById, params).then((res) => {
          if (res.success) {
            this.edit(res.result)
          }
        })
      }
    },
    validateError(msg) {
      this.$message.error(msg)
    },
    popupCallback(row) {
      this.form.setFieldsValue(pick(row, 'qbsName', 'supervisor', 'qbsCode', 'rectificationCompleteDate', 'evaluationState', 'quantityWork', 'quantityWorkUnit', 'constructionStartDate', 'constructionEndDate', 'constructionUnit', 'pictureInformation', 'videoInformation', 'attachment'))
    },

    handleDelete(rowIndex) {
      if (this.$refs.qualityEvaluationInformationDetail) {
        const dataSource = [...this.qualityEvaluationInformationDetailTable.dataSource]
        if (typeof rowIndex === 'number' && rowIndex > -1 && rowIndex < dataSource.length) {
          dataSource.splice(rowIndex, 1)
          this.qualityEvaluationInformationDetailTable.dataSource = dataSource
          this.$refs.qualityEvaluationInformationDetail.removeRows([rowIndex])
          this.$message.success('删除成功')
        } else {
          console.log('Invalid index:', rowIndex, 'DataSource length:', dataSource.length)
          this.$message.error('删除失败：无效的行索引')
        }
      }
    }
  }
}
</script>

<style scoped>
</style>