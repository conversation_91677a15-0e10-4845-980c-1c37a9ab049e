<template>
  <j-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :maskClosable="false"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel">
    <Form ref="realForm" @ok="submitCallback" :disabled="disableSubmit" />
    <template slot="footer">
      <a-button @click="handleOk"> 保存</a-button>
      <a-button type="primary" @click="handleSubmit"> 提交</a-button>
    </template>
  </j-modal>
</template>

<script>

import Form from './Form'

export default {
  name: 'QualityEvaluationInformationModal',
  components: {
    Form
  },
  data() {
    return {
      title: '',
      width: 1200,
      visible: false,
      disableSubmit: false,
      issubmit: false //提交
    }
  },
  methods: {
    add() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.add()
      })
    },
    edit(record) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.realForm.edit(record)
      })
    },
    close() {
      this.$emit('close')
      this.visible = false
    },
    handleSubmit() {
      this.issubmit = true
      this.$refs.realForm.submitForm()
    },
    handleOk() {
      this.$refs.realForm.submitForm()
    },
    submitCallback(dataid) {
      console.log("submitCallback",dataid,this.issubmit)
      let id = ''
      if (dataid instanceof Object) {
        id = dataid.id
      } else {
        id = dataid
      }
      if (this.issubmit) {
        if (id) this.$emit('submit', id)
      } else {
        this.$emit('ok')
      }
      this.visible = false
    },
    handleCancel() {
      this.close()
    }
  }
}
</script>

<style scoped>
</style>